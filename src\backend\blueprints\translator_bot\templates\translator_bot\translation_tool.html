<!-- templates/translator_bot/translation_tool.html -->

{% extends "base.html" %}

{% block head_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="{{ url_for('static', filename='translator_tool.css') }}">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
{% endblock %}

{% block content %}
<div class="translation-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="translation-card">
                    <div class="card-header-custom position-relative text-center" style="display: flex; flex-direction: column; align-items: center; padding-top: 0;">
                        <span class="header-icon-top" style="font-size: 5rem; margin-bottom: 0.1rem; margin-top: 0; display: block;">
                            <i class="fas fa-language"></i>
                        </span>
                        <h1 class="mb-0" style="font-size: 2.5rem; font-weight: 700;">Document Translation Tool</h1>
                        <p class="mb-0 mt-2 opacity-75">
                            Translate documents with AI
                        </p>
                    </div>
                    
                    <div class="card-body p-4">
                        <!-- File Upload Section -->
                        <div class="mb-4 mt-4">                            
                            <div class="upload-area" id="uploadArea">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h6>Drag & drop your file here</h6>
                                <p class="text-muted mb-3">or click to browse</p>
                                <p class="small text-muted">
                                    Supported formats: Excel (.xlsx), PowerPoint (.pptx), Word (.docx), PDF (.pdf)
                                    <br>Maximum file size: 100MB
                                </p>
                                <input type="file" id="fileInput" accept=".xlsx,.pptx,.docx,.pdf" style="display: none;">
                            </div>
                            
                            <div id="fileInfo" class="file-info" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file me-3 text-dark"></i>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold" id="fileName"></div>
                                        <div class="small text-muted" id="fileDetails"></div>
                                    </div>
                                    <button class="remove-file-btn" id="removeFile" type="button">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Loading Spinner -->
                            <div id="loadingSpinner" class="loading-spinner">
                                <div class="spinner"></div>
                                <div class="loading-text" id="loadingText">Uploading and processing file...</div>
                            </div>
                        </div>

                        <!-- File Options (Excel columns, Word paragraphs, PPTX slides) -->
                        <div id="fileOptions" class="excel-options" style="display: none;">
                            <div class="excel-options-header">
                                <h5 id="fileOptionsHeader"></h5>
                                <button type="button" class="select-all-btn" id="toggleAllColumns" style="display:none;"><i class="fas fa-check-square me-1"></i>Deselect All</button>
                            </div>
                            <p class="text-muted mb-3" id="fileOptionsDescription"></p>
                            <div class="row" id="optionCheckboxes">
                                <!-- Checkboxes will be populated dynamically -->
                            </div>
                        </div>

                        <!-- File Context Input & Language Selection -->
                        <div id="languageSettings" class="excel-options mt-4" style="display: none;">
                            <div class="excel-options-header">
                                <h5>
                                    <i class="fas fa-globe me-2"></i>
                                    Language Settings
                                </h5>
                            </div>
                            <!-- Context Input -->
                            <div class="mb-3">
                                <label for="fileContext" class="form-label fw-semibold">File Context (optional)</label>
                                <textarea class="form-control" id="fileContext" rows="3" placeholder="Add any relevant context or instructions for the model to improve the translation accuracy."></textarea>
                            </div>
                            <div class="language-grid">
                                <div>
                                    <label for="sourceLanguage" class="form-label fw-semibold">Source Language</label>
                                    <select class="form-select form-select-custom" id="sourceLanguage">
                                        <option value="auto">Auto-detect</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="zh">Chinese</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="targetLanguage" class="form-label fw-semibold">Target Language(s)</label>
                                    <select class="form-select form-select-custom" id="targetLanguage" multiple="multiple" style="width: 100%;">
                                        <option value="ar">Arabic</option>
                                        <option value="zh">Chinese</option>
                                        <option value="da">Danish</option>
                                        <option value="nl">Dutch</option>
                                        <option value="en">English</option>
                                        <option value="fi">Finnish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="hi">Hindi</option>
                                        <option value="it">Italian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="no">Norwegian</option>
                                        <option value="pl">Polish</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="es">Spanish</option>
                                        <option value="sv">Swedish</option>
                                        <option value="th">Thai</option>
                                        <option value="tu">Turkish</option>
                                        <option value="vi">Vietnamese</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Section -->
                        <div id="previewSection" class="excel-options mt-4" style="display: none;">
                            <div class="excel-options-header">
                                <h5>
                                    <i class="fas fa-eye me-2"></i>
                                    Translation Preview
                                </h5>
                            </div>
                            <p class="text-muted mb-3">Preview of the translation in <span id="previewTargetLanguage"></span> of random cells taken from the <span id="previewColumnCount">1</span> selected column(s):</p>

                            <div class="preview-table-container">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th style="width: 8%;">Row</th>
                                            <th style="width: 15%;">Column</th>
                                            <th style="width: 38%;">Original</th>
                                            <th style="width: 39%;">Translated</th>
                                        </tr>
                                    </thead>
                                    <tbody id="previewTableBody">
                                        <!-- Preview data will be populated here -->
                                    </tbody>
                                </table>
                            </div>

                            <div class="preview-actions text-center mt-4">
                                <button class="btn btn-outline-secondary me-3" id="newPreviewBtn">
                                    <i class="fas fa-redo me-2"></i>
                                    New Preview
                                </button>
                                <button class="btn btn-success btn-lg" id="continueTranslationBtn">
                                    <i class="fas fa-check me-2"></i>
                                    Continue with Full Translation
                                </button>
                            </div>
                        </div>

                        <!-- Translation Button -->
                        <div class="text-center mt-4" id="translateButtonSection">
                            <button class="btn btn-outline-primary me-3" id="previewBtn" style="display: none;">
                                <i class="fas fa-eye me-2"></i>
                                Preview Translation
                            </button>
                            <button class="translate-link-custom btn-lg" id="translateBtn" disabled>
                                <i class="fas fa-magic me-2"></i>
                                Start Translation
                            </button>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block tail_js %}
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 for targetLanguage
    $('#targetLanguage').select2({
        placeholder: 'Select target language(s)',
        allowClear: true,
        width: 'resolve'
    });


    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileDetails = document.getElementById('fileDetails');
    const removeFile = document.getElementById('removeFile');
    const fileOptions = document.getElementById('fileOptions');
    const optionCheckboxes = document.getElementById('optionCheckboxes');
    const toggleAllColumns = document.getElementById('toggleAllColumns');
    const translateBtn = document.getElementById('translateBtn');
    const targetLanguage = document.getElementById('targetLanguage');

    const loadingSpinner = document.getElementById('loadingSpinner');
    const loadingText = document.getElementById('loadingText');
    const languageSettings = document.getElementById('languageSettings');
    const previewSection = document.getElementById('previewSection');
    const previewBtn = document.getElementById('previewBtn');
    const newPreviewBtn = document.getElementById('newPreviewBtn');
    const continueTranslationBtn = document.getElementById('continueTranslationBtn');

    let selectedFile = null;
    let fileType = null;
    let currentSessionId = null; // Track current translation session
    let previewData = null; // Store current preview data

    // Ensure the translate button is always disabled on page load
    translateBtn.hidden = true;

    // Note: File cleanup now only happens on new file upload (server-side)
    // Removed automatic cleanup on page unload/visibility change to preserve user files

    // Helper function to show alerts
    function showAlert(message, type = 'info') {
        // Create a simple alert div
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    // Upload area click handler
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });

    // Drag and drop handlers
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });

    // File input change handler
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    // Remove file handler
    removeFile.addEventListener('click', () => {
        // Note: File cleanup now only happens on new file upload
        // Removed cleanup here to preserve user files unless they upload a new one
        console.log('removeFile clicked - UI reset only, no file cleanup');
        
        selectedFile = null;
        currentSessionId = null;
        translationCompleted = false;
        fileInfo.style.display = 'none';
        uploadArea.style.display = 'block';
        fileOptions.style.display = 'none';
        loadingSpinner.style.display = 'none';
        fileInput.value = '';
        languageSettings.style.display = 'none';
        const existingDownloadLinks = document.querySelectorAll('.download-link-custom, .new-translation-btn');
        existingDownloadLinks.forEach(link => link.remove());
        updateTranslateButton();
    });

    // Target language change handler (Select2 event)
    $('#targetLanguage').on('change', function() {
        updateTranslateButton();
        showTranslateButtonAfterChange();
    });

    // Update button state when columns are selected/deselected
    optionCheckboxes.addEventListener('change', function() {
        updateTranslateButton();
        showTranslateButtonAfterChange();
    });

    // Context change handler
    const fileContextTextarea = document.getElementById('fileContext');
    fileContextTextarea.addEventListener('input', showTranslateButtonAfterChange);

    // Source language change handler
    const sourceLanguageSelect = document.getElementById('sourceLanguage');
    sourceLanguageSelect.addEventListener('change', showTranslateButtonAfterChange);

    // Translate button handler
    translateBtn.addEventListener('click', startTranslation);

    // Preview button handlers
    previewBtn.addEventListener('click', showTranslationPreview);
    newPreviewBtn.addEventListener('click', showTranslationPreview);
    continueTranslationBtn.addEventListener('click', startTranslation);

    // Toggle all columns button handler
    toggleAllColumns.addEventListener('click', function() {
        const checkboxContainer = document.getElementById('columnCheckboxes');
        if (!checkboxContainer) return;
        const checkboxes = checkboxContainer.querySelectorAll('input[type="checkbox"]');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });

        // Update button text and icon safely
        let icon = this.querySelector('i');
        if (allChecked) {
            this.innerHTML = '<i class="fas fa-square me-1"></i>Select All';
        } else {
            this.innerHTML = '<i class="fas fa-check-square me-1"></i>Deselect All';
        }
        updateTranslateButton();
    });

    function handleFileSelect(file) {
        // Note: File cleanup now happens automatically on server-side during new upload
        // No need for manual cleanup here
        
        // Automatically detect file type by extension
        const allowedTypes = ['.xlsx', '.pptx', '.docx', '.pdf'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        fileType = fileExtension;
        if (!allowedTypes.includes(fileExtension)) {
            showAlert('Please select a valid file type: Excel (.xlsx), PowerPoint (.pptx), Word (.docx) or PDF (.pdf)', 'warning');
            return;
        }
        if (file.size > 100 * 1024 * 1024) {
            showAlert('File size must be less than 100MB', 'warning');
            return;
        }
        selectedFile = file;
        currentSessionId = null; // Reset session ID for new file
        translationCompleted = false;
        languageSettings.style.display = 'none';
        uploadArea.style.display = 'none';
        loadingSpinner.style.display = 'block';
        loadingText.textContent = 'Uploading and processing file...';
        // All logic is now automatic based on file extension
        if (fileExtension === '.xlsx' || fileExtension === '.pptx' || fileExtension === '.docx' || fileExtension === '.pdf') {
            uploadFileToServer(file);
        } else {
            setTimeout(() => {
                loadingSpinner.style.display = 'none';
                fileName.textContent = file.name;
                fileDetails.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB • ${fileExtension.toUpperCase().substring(1)} file`;
                fileInfo.style.display = 'block';
                showFileOptions(fileExtension);
                updateTranslateButton();
            }, 800);
        }
    }

    function uploadFileToServer(file) {
        const formData = new FormData();
        formData.append('file', file);
        
        loadingText.textContent = 'Uploading file...';

        fetch('/translator/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.type == '.xlsx') {
                loadingText.textContent = 'Getting Excel table...';
                getExcelColumns();
                }
                else if (data.type == '.pptx') {
                loadingText.textContent = 'Getting PowerPoint slides...';
                // TODO
                }
                else if (data.type == '.docx') {
                loadingText.textContent = 'Getting Word paragraphs...';
                // TODO
                }
                else if (data.type == '.pdf'){
                loadingText.textContent = 'Getting PDF paragraphs...';
                }
                finishFileLoading(data.type);
            } else {
                loadingSpinner.style.display = 'none';
                uploadArea.style.display = 'block';
                
                // Reset file selection state
                selectedFile = null;
                currentSessionId = null;
                fileInput.value = '';
                
                showAlert('Error uploading file: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            loadingSpinner.style.display = 'none';
            uploadArea.style.display = 'block';
            
            // Reset file selection state
            selectedFile = null;
            currentSessionId = null;
            fileInput.value = '';
            
            showAlert('Error uploading file', 'danger');
        });
    }

    function finishFileLoading(fileExtension) {
        // Hide loading spinner and show file info
        loadingSpinner.style.display = 'none';
        // Show language settings now that upload/processing is done
        languageSettings.style.display = 'block';
        // Update file info display
        fileName.textContent = selectedFile.name;
        fileInfo.style.display = 'block';
        updateTranslateButton();
    }

    function getExcelColumns() {
        loadingText.textContent = 'Analyzing Excel columns...';
        
        fetch('/translator/api/columns', {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Use generic file options function for all file types
                showFileOptions('.xlsx', data.columns);
                finishFileLoading();
            } else {
                console.error('Error getting columns:', data.error);
                console.log(data);
                
                // Handle specific file not found errors
                const errorMessage = data.error || 'Unknown error';
                if (errorMessage.includes('not found') || errorMessage.includes('No such file')) {
                    showAlert('The uploaded file is no longer available. Please upload the file again.', 'warning');
                    // Reset the interface to allow re-upload
                    selectedFile = null;
                    currentSessionId = null;
                    translationCompleted = false;
                    fileInfo.style.display = 'none';
                    uploadArea.style.display = 'block';
                    fileOptions.style.display = 'none';
                    languageSettings.style.display = 'none';
                    fileInput.value = '';
                } else {
                    showAlert('Error analyzing Excel file: ' + errorMessage, 'danger');
                    uploadArea.style.display = 'block';
                }
                
                loadingSpinner.style.display = 'none';
                updateTranslateButton();
            }
        })
        .catch(error => {
            console.error('Get columns error:', error);
            loadingSpinner.style.display = 'none';
            uploadArea.style.display = 'block';
            showAlert('Error analyzing Excel file', 'danger');
        });
    }

    function showFileOptions(fileExtension, options = null) {
        const fileOptionsHeader = document.getElementById('fileOptionsHeader');
        const fileOptionsDescription = document.getElementById('fileOptionsDescription');
        optionCheckboxes.innerHTML = '';
        let opts = [];
        if (fileExtension === '.xlsx') {
            fileOptionsHeader.textContent = 'Select columns to translate:';
            fileOptionsDescription.textContent = 'Select which columns you want to translate.';
            opts = options || [];
            toggleAllColumns.style.display = opts.length > 1 ? 'inline-block' : 'none';
        } else if (fileExtension === '.docx') {
            fileOptionsHeader.textContent = 'Select paragraphs to translate:';
            fileOptionsDescription.textContent = 'Select which paragraphs you want to translate.';
            opts = ['All Paragraphs'];
            toggleAllColumns.style.display = 'none';
        } else if (fileExtension === '.pptx') {
            fileOptionsHeader.textContent = 'Select slides to translate:';
            fileOptionsDescription.textContent = 'Select which slides you want to translate.';
            opts = ['All Slides'];
            toggleAllColumns.style.display = 'none';
        } else if (fileExtension == '.pdf'){
            ileOptionsHeader.textContent = 'Select paragraphs to translate:';
            fileOptionsDescription.textContent = 'Select which paragraphs you want to translate.';
            opts = ['All Paragraphs'];
            toggleAllColumns.style.display = 'none';
        }
        const checkboxContainer = document.createElement('div');
        checkboxContainer.className = 'row';
        checkboxContainer.id = 'columnCheckboxes'; // Assign id for later reference
        opts.forEach((option, index) => {
            const colDiv = document.createElement('div');
            colDiv.className = 'col-md-6 column-checkbox';
            colDiv.innerHTML = `
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${option}" id="opt${index}" checked>
                    <label class="form-check-label" for="opt${index}">
                        ${option}
                    </label>
                </div>
            `;
            checkboxContainer.appendChild(colDiv);
        });
        optionCheckboxes.appendChild(checkboxContainer);
        fileOptions.style.display = 'block';
        languageSettings.style.display = 'block';
        translateBtn.hidden = false;
    }
    
    function showTranslationPreview() {
        const selectedLangs = getSelectedTargetLanguages();
        if (!selectedFile || selectedLangs.length === 0) {
            alert('Please select a file and at least one target language');
            return;
        }

        // Get selected columns
        const checkboxContainer = document.getElementById('columnCheckboxes');
        if (!checkboxContainer) {
            alert('No columns found');
            return;
        }
        const checkboxes = checkboxContainer.querySelectorAll('input[type="checkbox"]:checked');
        const selectedColumns = Array.from(checkboxes).map(cb => cb.value);

        if (selectedColumns.length === 0) {
            alert('Please select at least one column to translate');
            return;
        }

        // Preview all selected columns for the first target language
        const targetLangToPreview = selectedLangs[0];

        // Show loading state for both preview and new preview buttons, and disable translate button
        previewBtn.disabled = true;
        previewBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating Preview...';
        newPreviewBtn.disabled = true;
        newPreviewBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating Preview...';
        translateBtn.disabled = true;

        // Call preview API with all selected columns
        const fileContext = document.getElementById('fileContext').value;
        fetch('/translator/api/preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                column_names: selectedColumns,  // Send all selected columns
                target_language: getLanguageName(targetLangToPreview),
                file_context: fileContext
            })
        })
        .then(response => response.json())
        .then(data => {
            // Reset button states
            previewBtn.disabled = false;
            previewBtn.innerHTML = '<i class="fas fa-eye me-2"></i>Preview Translation';
            newPreviewBtn.disabled = false;
            newPreviewBtn.innerHTML = '<i class="fas fa-redo me-2"></i>New Preview';

            console.log('Preview API response:', data);

            if (data.success) {
                displayPreview(data);
            } else {
                alert('Preview failed: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Preview error:', error);
            // Reset button states on error
            previewBtn.disabled = false;
            previewBtn.innerHTML = '<i class="fas fa-eye me-2"></i>Preview Translation';
            newPreviewBtn.disabled = false;
            newPreviewBtn.innerHTML = '<i class="fas fa-redo me-2"></i>New Preview';
            alert('Preview failed: ' + error.message);
        });
    }

    function displayPreview(data) {
        console.log('Displaying preview with data:', data);
        previewData = data;

        // Update preview section content
        const columnNames = data.column_names || [data.column_name];
        const columnCount = columnNames.length;

        document.getElementById('previewColumnCount').textContent = columnCount;
        document.getElementById('previewTargetLanguage').textContent = data.target_language;

        // Populate preview table
        const tableBody = document.getElementById('previewTableBody');
        tableBody.innerHTML = '';

        console.log('Preview data array:', data.preview_data);
        console.log('Preview data length:', data.preview_data ? data.preview_data.length : 'undefined');

        if (data.preview_data && data.preview_data.length > 0) {
            // Group data by column to add visual separation
            const groupedData = {};
            data.preview_data.forEach(row => {
                if (!groupedData[row.column]) {
                    groupedData[row.column] = [];
                }
                groupedData[row.column].push(row);
            });

            Object.keys(groupedData).forEach((columnName, columnIndex) => {
                // Add a separator row between columns (except for the first column)
                if (columnIndex > 0) {
                    const separatorTr = document.createElement('tr');
                    separatorTr.className = 'column-separator';
                    separatorTr.innerHTML = `
                        <td colspan="4" class="text-center text-muted py-2" style="background-color: #f8f9fa; border-top: 2px solid #dee2e6; font-weight: 500;">
                        </td>
                    `;
                    tableBody.appendChild(separatorTr);
                }

                // Add rows for this column - use actual Excel row numbers
                groupedData[columnName].forEach((row, rowIndex) => {
                    console.log(`Processing Excel row ${row.row} for column ${columnName}:`, row);
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td class="fw-bold">${row.row}</td>
                        <td class="text-primary fw-semibold">${escapeHtml(row.column || 'N/A')}</td>
                        <td>${escapeHtml(row.original)}</td>
                        <td class="text-success">${escapeHtml(row.translated)}</td>
                    `;
                    tableBody.appendChild(tr);
                });
            });
        } else {
            // Add a message if no data
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td colspan="4" class="text-center text-muted">No preview data available</td>
            `;
            tableBody.appendChild(tr);
        }

        // Show preview section and hide translate button section
        previewSection.style.display = 'block';
        document.getElementById('translateButtonSection').style.display = 'none';

        // Scroll to preview section
        previewSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    function getLanguageName(code) {
        const languages = {
            'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
            'it': 'Italian', 'pt': 'Portuguese', 'ru': 'Russian', 'ja': 'Japanese',
            'ko': 'Korean', 'zh': 'Chinese', 'ar': 'Arabic', 'hi': 'Hindi',
            'th': 'Thai', 'vi': 'Vietnamese', 'nl': 'Dutch', 'sv': 'Swedish',
            'no': 'Norwegian', 'da': 'Danish', 'fi': 'Finnish', 'pl': 'Polish'
        };
        return languages[code] || code;
    }

    function getSelectedTargetLanguages() {
        // Returns an array of selected language codes from Select2
        return $('#targetLanguage').val() || [];
    }

    let translationCompleted = false; // Track if translation was completed

    function updateTranslateButton() {
        const hasFile = selectedFile !== null;
        const selectedLangs = getSelectedTargetLanguages();

        // Hide buttons if translation was completed and no changes made
        if (translationCompleted) {
            translateBtn.style.display = 'none';
            previewBtn.style.display = 'none';
            return;
        }

        // Show/hide translate button section
        document.getElementById('translateButtonSection').style.display = hasFile ? 'block' : 'none';

        // Update translate button
        translateBtn.hidden = !(hasFile);
        translateBtn.disabled = !(hasFile && selectedLangs.length > 0);

        // Update preview button (only show for Excel files with columns selected)
        const hasColumns = document.getElementById('columnCheckboxes') &&
                          document.querySelectorAll('#columnCheckboxes input[type="checkbox"]:checked').length > 0;
        const isExcelFile = fileType === '.xlsx';

        previewBtn.style.display = (hasFile && selectedLangs.length > 0 && isExcelFile && hasColumns) ? 'inline-block' : 'none';
        previewBtn.disabled = !(hasFile && selectedLangs.length > 0 && hasColumns);

        // Hide preview section when settings change
        if (previewSection.style.display === 'block') {
            previewSection.style.display = 'none';
            document.getElementById('translateButtonSection').style.display = 'block';
        }
    }

    function showTranslateButtonAfterChange() {
        // Show translate button again when user makes changes
        if (translationCompleted) {
            translationCompleted = false;
            updateTranslateButton();
        }

        // Hide preview section when settings change
        if (previewSection.style.display === 'block') {
            previewSection.style.display = 'none';
            document.getElementById('translateButtonSection').style.display = 'block';
        }
    }

    function startTranslation() {
        const selectedLangs = getSelectedTargetLanguages();
        if (!selectedFile || selectedLangs.length === 0) {
            alert('Please select a file and at least one target language');
            return;
        }
        
        // Remove any existing download links immediately when translation starts
        const existingLinks = document.querySelectorAll('.download-link-custom, .new-translation-btn');
        existingLinks.forEach(link => link.remove());
        
        // Get selected columns for Excel files
        let selectedColumns = [];
        if (fileType === '.xlsx') {
            const checkboxes = document.getElementById('columnCheckboxes').querySelectorAll('input[type="checkbox"]:checked');
            selectedColumns = Array.from(checkboxes).map(cb => cb.value);
        } else if (fileType === '.docx') {
            selectedColumns = ['All Paragraphs'];
        } else if (fileType === '.pptx') {
            selectedColumns = ['All Slides'];
        } else if (fileType === '.pdf'){
            selectedColumns = ['All Paragraphs'];
        }

        translateBtn.disabled = true;
        translateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Translating...';
        
        // Disable the remove file button during translation
        removeFile.disabled = true;
        removeFile.style.opacity = '0.5';
        removeFile.style.cursor = 'not-allowed';

        // Generate session ID for tracking
        const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        currentSessionId = sessionId; // Store the session ID

        // Start translation
        const fileContext = document.getElementById('fileContext').value;
        const translationData = {
            target_languages: selectedLangs,
            source_language: document.getElementById('sourceLanguage').value,
            selected_columns: selectedColumns,
            file_type: '.' + selectedFile.name.split('.').pop().toLowerCase(),
            original_filename: selectedFile.name,
            file_context: fileContext,
            session_id: sessionId
        };

        // Start the translation request
        fetch('/translator/api/translate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(translationData)
        })
        .then(response => response.json())
        .then(data => {
            console.log('Translation response received:', data); // Debug log

            translateBtn.disabled = false;
            translateBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Start Translation';
            
            // Re-enable the remove file button after translation
            removeFile.disabled = false;
            removeFile.style.opacity = '1';
            removeFile.style.cursor = 'pointer';

            if (data.success) {
                // Mark translation as completed IMMEDIATELY to prevent cleanup
                translationCompleted = true;
                console.log('Translation marked as completed, preventing cleanup');
                
                translateBtn.style.display = 'none';

                // Show success message with information about partial failures if applicable
                const requestedLangs = getSelectedTargetLanguages();
                const successfulLangs = data.languages || [];
                
                if (successfulLangs.length === requestedLangs.length) {
                    showAlert('Translation completed successfully!', 'success');
                } else if (successfulLangs.length > 0) {
                    const failedLangs = requestedLangs.filter(lang => !successfulLangs.includes(lang));
                    const failedLangNames = failedLangs.map(code => getLanguageName(code)).join(', ');
                    showAlert(`Translation partially completed. ${failedLangNames} translation(s) failed, but others succeeded.`, 'warning');
                } else {
                    showAlert('Translation failed for all languages.', 'danger');
                    return;
                }

                // Create download link only if we have successful translations
                if (successfulLangs.length > 0) {
                    const downloadLink = document.createElement('a');
                    downloadLink.className = 'btn btn-success btn-lg mt-3 download-link-custom';
                    downloadLink.style.display = 'block';
                    downloadLink.style.margin = '20px auto';
                    downloadLink.style.width = 'fit-content';

                    if (data.zip_file && successfulLangs.length > 1) {
                        // Multiple successful languages - show zip download
                        const zipFileParam = encodeURIComponent(data.zip_file);
                        downloadLink.href = `/translator/api/download/current?zip_file=${zipFileParam}`;
                        downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download All Translations (ZIP)';
                    } else if (successfulLangs.length === 1) {
                        // Single successful language - show single file download
                        const langParam = encodeURIComponent(successfulLangs[0]);
                        downloadLink.href = `/translator/api/download/current?lang=${langParam}`;
                        downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download Translated File';
                    } else {
                        // Multiple languages but no zip (shouldn't happen, but fallback)
                        const langParam = encodeURIComponent(successfulLangs[0]);
                        downloadLink.href = `/translator/api/download/current?lang=${langParam}`;
                        downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download Translated File';
                    }

                    // Insert download link after the translate button
                    const translateBtnElement = document.getElementById('translateBtn');
                    translateBtnElement.parentNode.insertBefore(downloadLink, translateBtnElement.nextSibling);
                    
                    // Add a "Translate Another File" button
                    const newTranslationBtn = document.createElement('button');
                    newTranslationBtn.className = 'btn btn-outline-primary btn-lg mt-3 new-translation-btn';
                    newTranslationBtn.style.display = 'block';
                    newTranslationBtn.style.margin = '10px auto 0';
                    newTranslationBtn.style.width = 'fit-content';
                    newTranslationBtn.innerHTML = '<i class="fas fa-plus me-2"></i>Translate Another File';
                    newTranslationBtn.addEventListener('click', function() {
                        // Reset UI for new translation - cleanup handled by server on next upload
                        selectedFile = null;
                        currentSessionId = null;
                        translationCompleted = false;
                        fileInfo.style.display = 'none';
                        uploadArea.style.display = 'block';
                        fileOptions.style.display = 'none';
                        languageSettings.style.display = 'none';
                        fileInput.value = '';
                        // Remove all custom buttons and download links
                        const existingDownloadLinks = document.querySelectorAll('.download-link-custom, .new-translation-btn');
                        existingDownloadLinks.forEach(link => link.remove());
                        updateTranslateButton();
                    });
                    
                    // Insert the new translation button after the download link
                    downloadLink.parentNode.insertBefore(newTranslationBtn, downloadLink.nextSibling);
                }
            } else {
                // Handle specific file not found errors
                const errorMessage = data.error || 'Unknown error';
                if (errorMessage.includes('Package not found') || errorMessage.includes('file not found') || errorMessage.includes('No such file')) {
                    showAlert('The uploaded file is no longer available. Please upload the file again.', 'warning');
                    // Reset the interface to allow re-upload
                    selectedFile = null;
                    currentSessionId = null;
                    translationCompleted = false;
                    fileInfo.style.display = 'none';
                    uploadArea.style.display = 'block';
                    fileOptions.style.display = 'none';
                    languageSettings.style.display = 'none';
                    fileInput.value = '';
                    updateTranslateButton();
                } else {
                    showAlert('Translation failed: ' + errorMessage, 'danger');
                }
            }
        })
        .catch(error => {
            console.error('Translation error:', error);
            console.log('Error details:', error.message, error.stack); // More detailed error logging
            translateBtn.disabled = false;
            translateBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Start Translation';
            showAlert('Translation failed due to network error', 'danger');

            // Re-enable the remove file button after translation error
            removeFile.disabled = false;
            removeFile.style.opacity = '1';
            removeFile.style.cursor = 'pointer';
            
            // Note: File cleanup now only happens on new file upload
            // Removed cleanup here to preserve user files
            currentSessionId = null;
        });
    }
});
</script>
{% endblock %}
