import json
import pandas as pd
from openpyxl import load_workbook
import os
import shutil
from typing import List, Dict, Tuple, Callable, Any
from flask import current_app
from src.backend.blueprints.translator_bot.memory_optimizer import MemoryOptimizationMixin


class ExcelHandler(MemoryOptimizationMixin):
    """
    Handles Excel file operations for the translation service.
    Separated from the main translation service to focus on Excel-specific functionality.
    """

    def __init__(self, excel_path: str):
        """
        Initialize the Excel handler with the path to the Excel file.

        Args:
            excel_path (str): Path to the Excel file
        """
        super().__init__()
        self.excel_path = excel_path
        self._initialize_memory_optimization(excel_path)


    def get_batches(self, selected_columns=None, max_rows=200, random_sample=False):
        """
        Generate JSON batches for selected columns with memory optimization.
        Args:
            selected_columns (list): List of column names to process. If None, use all columns.
            max_rows (int): Maximum number of rows per batch.
            random_sample (bool): If True, select random rows instead of sequential rows.
        Returns:
            List[Dict]: List of JSON batch objects for translation.
        """
        # Check if file exists before attempting to read
        if not os.path.exists(self.excel_path):
            raise FileNotFoundError(f"Excel file not found: {self.excel_path}")

        with self._memory_monitor_context("Excel batch generation") as monitor:
            # Use adaptive batch sizing based on memory conditions
            adaptive_max_rows = self._get_adaptive_batch_size(max_rows, "cell")
            current_app.logger.info(f"Using adaptive batch size: {adaptive_max_rows} rows (requested: {max_rows})")

            # Read Excel file with memory monitoring
            df = pd.read_excel(self.excel_path)
            columns = selected_columns if selected_columns else df.columns.tolist()
            batches = []

            total_cells = len(df) * len(columns)
            current_app.logger.info(f"Processing {len(df)} rows × {len(columns)} columns = {total_cells} cells")

            for col_idx, col in enumerate(columns):
                col_data = df[col].fillna("").astype(str)

                if random_sample and len(col_data) > adaptive_max_rows:
                    # For random sampling, select random rows from the entire column
                    import random
                    random.seed(42)  # Set seed for reproducible results

                    # Get random indices
                    total_rows = len(col_data)
                    sample_size = min(adaptive_max_rows, total_rows)
                    random_indices = sorted(random.sample(range(total_rows), sample_size))

                    # Create a single batch with random rows
                    sampled_data = col_data.iloc[random_indices]
                    json_obj = {
                        "column": col,
                        "content": {str(random_indices[j]+1): value for j, value in enumerate(sampled_data)}
                    }
                    batches.append(json_obj)
                else:
                    # Original sequential processing for full translation
                    for i in range(0, len(col_data), adaptive_max_rows):
                        chunk = col_data.iloc[i:i+adaptive_max_rows]
                        json_obj = {
                            "column": col,
                            "content": {str(j+1+i): value for j, value in enumerate(chunk)}
                        }
                        batches.append(json_obj)

                        # Update memory monitoring
                        if i % (adaptive_max_rows * 3) == 0:
                            monitor.update_peak()

                # Cleanup after each column for large spreadsheets
                if self._should_cleanup_memory(col_idx, self._adaptive_cleanup_frequency(len(columns), 5)):
                    self._force_garbage_collection()

            # Clear DataFrame from memory
            del df
            self._force_garbage_collection()

        current_app.logger.info(f"Generated {len(batches)} batches for {len(columns)} columns")
        return batches

    def get_batches_multi_language(self, selected_columns=None, max_rows=200, target_languages=None):
        """
        Optimized batch generation for multi-language translation.
        Generates batches once and reuses them for all target languages.

        Args:
            selected_columns (list): List of column names to process
            max_rows (int): Maximum number of rows per batch
            target_languages (list): List of target languages

        Returns:
            Dict[str, List[Dict]]: Batches organized by language
        """
        if not target_languages:
            return {"default": self.get_batches(selected_columns, max_rows)}

        with self._memory_monitor_context(f"Multi-language batch generation ({len(target_languages)} languages)") as monitor:
            # Generate base batches once
            base_batches = self.get_batches(selected_columns, max_rows)

            # Create language-specific batch sets (lightweight copies)
            language_batches = {}
            for lang in target_languages:
                # Create shallow copies to avoid memory duplication
                language_batches[lang] = [batch.copy() for batch in base_batches]
                monitor.update_peak()

            current_app.logger.info(f"Generated batches for {len(target_languages)} languages: {len(base_batches)} batches each")
            return language_batches

    
    def write_results_to_file(self, results: List[str], lang: str, header: str = None, selected_columns: List[str] = None):
        """
        Write the results to new columns in the Excel file with memory optimization.

        Args:
            results (List[str]): List of results to write (each result corresponds to a batch)
            lang (str): Target language code
            header (str, optional): Header value for the output column
            selected_columns (List[str], optional): List of selected columns to match with results
        """
        with self._memory_monitor_context(f"Excel write results ({len(results)} batches)") as monitor:
            current_app.logger.info(f"Writing results to Excel for language: {lang}, header: {header}")

            # Load the workbook and get the active sheet
            workbook = load_workbook(self.excel_path)
            sheet = workbook.active

            # Get the original DataFrame to determine starting column index
            df = pd.read_excel(self.excel_path)
            starting_col_idx = len(df.columns) + 1

            # Get all batches to understand the structure (use cached if available)
            all_columns = selected_columns if selected_columns else df.columns.tolist()
            batches = self.get_batches(all_columns)

            if len(results) != len(batches):
                current_app.logger.warning(f"Mismatch between results ({len(results)}) and batches ({len(batches)})")

            # Clear DataFrame early to free memory
            del df
            self._force_garbage_collection()

        # Group results by column name to handle multiple columns
        column_results = {}
        for i, result in enumerate(results):
            try:
                # Parse the result as JSON to extract the values
                result_json = json.loads(result)
                current_app.logger.debug(f"Processing result batch {i+1}: {len(result_json)} items")

                # Get column name from corresponding batch
                if i < len(batches):
                    column_name = batches[i].get('column', f'Column_{i}')
                else:
                    column_name = f'Column_{i}'

                if column_name not in column_results:
                    column_results[column_name] = {}

                # Merge the results for this column
                for row_idx, value in result_json.items():
                    column_results[column_name][row_idx] = value

            except json.JSONDecodeError as e:
                current_app.logger.warning(f"Invalid JSON in result batch {i+1}: {e}")
                # If the result is not valid JSON, treat as a single value
                if i < len(batches):
                    column_name = batches[i].get('column', f'Column_{i}')
                else:
                    column_name = f'Column_{i}'
                if column_name not in column_results:
                    column_results[column_name] = {}
                column_results[column_name][str(i+1)] = result
            except Exception as e:
                current_app.logger.error(f"Error processing result batch {i+1}: {e}")

        # Write each column's results to a separate Excel column
        current_col_idx = starting_col_idx
        total_values_written = 0

        for column_name, column_data in column_results.items():
            # Write the header first
            header_text = f"{column_name}_{lang}_translated" if header is None else f"{header}_{lang}_translated"
            sheet.cell(row=1, column=current_col_idx, value=header_text)
            current_app.logger.info(f"Written header '{header_text}' to column {current_col_idx}")

            # Write the results for this column
            for row_idx, value in column_data.items():
                actual_row = int(row_idx) + 1  # +1 for header row
                sheet.cell(row=actual_row, column=current_col_idx, value=value)
                total_values_written += 1

            current_app.logger.info(f"Written {len(column_data)} values for column '{column_name}' to Excel column {current_col_idx}")
            current_col_idx += 1

        current_app.logger.info(f"Written {total_values_written} total values across {len(column_results)} columns")

        # Save the workbook to a new file for the language
        import os
        original_base, original_ext = os.path.splitext(self.excel_path)
        translated_path = f"{original_base}_{lang}{original_ext}"
        
        # Ensure the directory exists
        directory = os.path.dirname(translated_path)
        if not os.path.exists(directory):
            current_app.logger.info(f"Creating directory: {directory}")
            os.makedirs(directory, exist_ok=True)
        
        try:
            workbook.save(translated_path)
            current_app.logger.info(f"Successfully saved translated Excel file: {translated_path}")
        except Exception as e:
            current_app.logger.error(f"Error saving translated Excel file: {e}")
            raise


    def get_excel_info(self) -> Dict:
        """
        Get Excel file information including columns and row count.

        Returns:
            Dict: Dictionary containing columns list and row count
        """
        try:
            # Check if file exists before attempting to read
            if not os.path.exists(self.excel_path):
                raise FileNotFoundError(f"Excel file not found: {self.excel_path}")
                
            df = pd.read_excel(self.excel_path)
            return {
                'col_names': df.columns.tolist(),
                'col_count': len(df.columns),
                'row_count': len(df),
            }
        except Exception as e:
            current_app.logger.error(f"Error reading Excel info: {e}")
            return {
                'col_names': [],
                'col_count': 0,
                'row_count': 0,
                'error': str(e)
            }

    def get_next_available_column_letter(self) -> str:
        """
        Get the next available column letter for writing results.

        Returns:
            str: Next available column letter (e.g., 'C', 'D', etc.)
        """
        try:
            df = pd.read_excel(self.excel_path)
            return chr(ord('A') + len(df.columns))
        except Exception as e:
            current_app.logger.error(f"Error determining next column: {e}")
            return 'B'  # Default fallback
    
    
    def get_total_batches(self, selected_columns, target_languages, max_rows=200):
        excel_info = self.get_excel_info()
        columns = len(selected_columns) if selected_columns else len(excel_info.get('col_names', []))
        if target_languages and isinstance(target_languages, list) and len(target_languages):
            return len(target_languages)
        else:
            return columns