# word_handler.py
import json
import os
import re
import time
import traceback
from typing import Dict, List, Tuple

from docx import Document
from docx.text.paragraph import Paragraph
from docx.oxml.ns import qn
from lxml import etree

from src.agents.eproexcella_agent.models.testual import TextTranslator
from src.backend.blueprints.translator_bot.translator import Translator
from src.backend.blueprints.translator_bot.memory_optimizer import MemoryOptimizationMixin
from utils.core import get_logger
from pdf2docx import Converter

logger = get_logger(__file__)


class EnhancedTextMerger:
    """
    Helper class for intelligent text merging in PDF-converted documents.
    Conservative merging logic to avoid creating incorrect sentence joins.
    """

    def __init__(self):
        # Precompiled patterns
        self._sentence_endings = re.compile(r"[.!?]+\s*$")
        self._hyphen_split = re.compile(r"(\w)-\s+(\w)", re.IGNORECASE)
        self._hyphen_trailing = re.compile(r"-\s*$")
        # Continuation words per language (small list, used for heuristics)
        self.continuation_words = {
            "en": ["and", "or", "but", "with", "without", "in", "on", "at", "to", "for", "of", "by", "the"],
            "it": ["e", "o", "ma", "con", "senza", "in", "su", "a", "per", "di"],
            "es": ["y", "o", "pero", "con", "sin", "en", "sobre", "a", "para", "de"],
            "fr": ["et", "ou", "mais", "avec", "sans", "dans", "sur", "à", "pour"],
            # add more as needed...
        }

        # Simple verb indicators for 'complete sentence' heuristic
        self._verb_indicators = {
            "en": ["is", "are", "was", "were", "have", "has", "had", "will", "would", "can", "could"],
            "it": ["è", "sono", "era", "erano", "ha", "hanno"],
        }

    def merge_hyphenated_words(self, text: str) -> str:
        """Merge hyphen-split words across lines."""
        if not text:
            return text
        return self._hyphen_split.sub(r"\1\2", text)

    def _looks_like_complete_sentence(self, text: str) -> bool:
        """Heuristic: ends with punctuation or contains a verb indicator and is long enough."""
        if not text:
            return False
        t = text.strip()
        if not t:
            return False
        if t[-1] in ".!?":
            return True
        if len(t.split()) < 3 or len(t) < 15:
            return False
        low = t.lower()
        # check any language indicators
        if any(v in low for v in self._verb_indicators.get("en", [])):
            return True
        return False

    def _looks_like_title_or_header(self, text: str) -> bool:
        """Avoid merging probable titles/headers."""
        if not text:
            return False
        t = text.strip()
        if len(t) > 3 and t.isupper():
            return True
        if len(t) < 80 and t.istitle() and t[-1] not in ".!?:;,":
            return True
        if re.search(r"^\d+\.?\s+[A-Z]", t):
            return True
        return False

    def should_merge_paragraphs(self, prev_text: str, curr_text: str, language: str = "en") -> bool:
        """Decide conservatively whether two fragments should be merged."""
        if not prev_text or not curr_text:
            return False
        prev = prev_text.strip()
        curr = curr_text.strip()
        # Unisci solo parole spezzate da trattino a fine riga
        return bool(self._hyphen_trailing.search(prev))


class WordHandler(MemoryOptimizationMixin):
    """
    Enhanced Word document handler with improved PDF fragmentation handling and memory optimization.
    Public methods kept unchanged: translate_document, write_result_to_file,
    convert_to_word, get_translation_stats.
    """

    def __init__(self, docx_path: str, file_context, source_language: str = 'auto'):
        super().__init__()
        self.docx_path = docx_path
        self.file_context = file_context
        self.source_language = source_language
        self.original_name = ''
        self.text_merger = EnhancedTextMerger()
        self.is_pdf_converted = False
        self._initialize_memory_optimization(docx_path)

        # Precompile regexes used across methods
        self.url_pattern = re.compile(
            r"http[s]?://(?:[a-zA-Z0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+"
        )
        self.email_pattern = re.compile(
            r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
        )
        self.phone_pattern = re.compile(r"[\+]?[1-9]?[0-9]{7,15}")
        self.file_path_pattern = re.compile(r"[A-Za-z]:\\(?:[^\\/:*?\"<>|\r\n]+\\)*[^\\/:*?\"<>|\r\n]*")
        self.bullet_pattern = re.compile(r"^[\u2022\u2023\u25E6\u2043\u2219\-\*]\s+")
        self.numbered_pattern = re.compile(r"^\d+[\.\)]\s+")
        self.lettered_pattern = re.compile(r"^[a-zA-Z][\.\)]\s+")
        self.only_numbers_pattern = re.compile(r"^[\d\s\-\./:\\]+$")

        # Document properties
        # self.is_pdf_converted = self._detect_pdf_conversion()
        self.document_language = "en"  # default, possibly updated later
        self._language_cached = False

    # ---------------------- Detection & Analysis ---------------------- #
    def _detect_pdf_conversion(self) -> bool:
        """
        Heuristic to detect whether the document was converted from PDF.
        """
        if self.original_name == '':
            return False
        else:
            return True

    def _has_inconsistent_spacing(self, prev_text: str, curr_text: str) -> bool:
        if not prev_text or not curr_text:
            return False
        if len(prev_text) < 50 and prev_text[-1] not in ".!?:;" and curr_text and curr_text[0].islower():
            return True
        return False

    def _analyze_document_structure(self, document: Document):
        """Collect basic stats and cache detected language."""
        try:
            total_paragraphs = len(document.paragraphs)
            empty_paragraphs = sum(1 for p in document.paragraphs if not p.text.strip())
            short_paragraphs = sum(
                1 for p in document.paragraphs if p.text.strip() and len(p.text.strip()) < 50
            )

            non_empty = [p for p in document.paragraphs if p.text.strip()]
            avg_length = (
                sum(len(p.text) for p in non_empty) / len(non_empty) if non_empty else 0
            )

            # Cache language detection only once
            if non_empty and not self._language_cached:
                sample = " ".join(p.text for p in non_empty[:10])
                self.document_language = self._detect_language(sample)
                self._language_cached = True
            self.is_pdf_converted = self._detect_pdf_conversion()
            logger.info("Document structure analysis:")
            logger.info("  - Total paragraphs: %s", total_paragraphs)
            logger.info("  - Empty paragraphs: %s", empty_paragraphs)
            logger.info("  - Short paragraphs (<50 chars): %s", short_paragraphs)
            logger.info("  - Average paragraph length: %.1f characters", avg_length)
            logger.info("  - PDF converted document: %s", self.is_pdf_converted)
            logger.info("  - Detected language: %s", self.document_language)
        except Exception as exc:
            logger.warning("Could not analyze document structure: %s", exc)

    def _detect_language(self, text: str) -> str:
        """Simple keyword-based detection (fast heuristic)."""
        if not text:
            return "en"
        t = text.lower()
        indicators = {
            "it": [" il ", " la ", " di ", " che ", " per ", " con ", " una "],
            "es": [" el ", " la ", " de ", " que ", " para "],
            "fr": [" le ", " la ", " de ", " que ", " pour "],
            "de": [" der ", " die ", " das ", " und ", " für "],
            "en": [" the ", " and ", " for ", " with ", " that "],
        }
        scores = {k: sum(1 for w in words if w in t) for k, words in indicators.items()}
        best = max(scores.items(), key=lambda x: x[1])
        return best[0] if best[1] > 0 else "en"

    # ---------------------- Translation Helpers ---------------------- #
    def _should_not_translate(self, text: str) -> bool:
        """Decide whether text should be left untouched."""
        if not text or not text.strip():
            return True
        t = text.strip()
        if self.url_pattern.search(t) or self.email_pattern.search(t) or self.phone_pattern.search(t):
            return True
        if self.file_path_pattern.search(t) or self.only_numbers_pattern.match(t):
            return True
        if len(t) <= 2:
            return True
        if t.isupper() and len(t.split()) <= 3:
            return True
        return False

    def _preserve_formatting_elements(self, text: str) -> Tuple[str, Dict]:
        """
        Replace elements that must be preserved (URLs, emails, phones) with placeholders.
        Returns cleaned text and mapping to restore later.
        """
        preserved = {}
        clean = text
        counter = 0

        for pat, tag in ((self.url_pattern, "URL"), (self.email_pattern, "EMAIL"), (self.phone_pattern, "PHONE")):
            for match in pat.finditer(clean):
                placeholder = f"__{tag}_{counter}__"
                preserved[placeholder] = match.group()
                clean = clean.replace(match.group(), placeholder)
                counter += 1

        return clean, preserved

    def _restore_formatting_elements(self, translated_text: str, preserved: Dict) -> str:
        result = translated_text
        for ph, orig in preserved.items():
            result = result.replace(ph, orig)
        return result

    def _batch_translate(self, texts: List[str], target_lang: str, batch_size: int = 50) -> List[str]:
        """
        Translate list of texts with memory optimization. Preserves items that should not be translated.
        Uses Translator().submit_to_gpt which returns JSON mapping '1': '...', etc.
        """
        if not texts:
            return []

        # Use adaptive batch sizing
        adaptive_batch_size = self._get_adaptive_batch_size(batch_size, "paragraph")
        logger.debug(f"Using adaptive batch size: {adaptive_batch_size} (requested: {batch_size})")

        all_trans = []
        translator = Translator()
        prompt = translator.get_default_prompt(target_lang, self.source_language)

        with self._memory_monitor_context(f"Word batch translation ({len(texts)} texts)") as monitor:
            # Process in adaptive batches to limit request sizes and memory usage
            for i in range(0, len(texts), adaptive_batch_size):
                batch_start_time = time.time()
                batch = texts[i : i + adaptive_batch_size]
                clean_batch = []
                preserved_batch = []

                # Prepare clean batch with placeholders
                for text in batch:
                    if self._should_not_translate(text):
                        clean_batch.append(text)
                        preserved_batch.append({})
                    else:
                        clean, pres = self._preserve_formatting_elements(text)
                        clean_batch.append(clean)
                        preserved_batch.append(pres)

                # Map to numbered keys as expected by translator
                dict_data = {str(j + 1): t for j, t in enumerate(clean_batch)}

                try:
                    response = translator.submit_to_gpt(dict_data, prompt, self.file_context)
                    parsed = json.loads(response) if isinstance(response, str) else response

                    for idx in range(len(clean_batch)):
                        key = str(idx + 1)
                        translated = parsed.get(key, clean_batch[idx])
                        if preserved_batch[idx]:
                            translated = self._restore_formatting_elements(translated, preserved_batch[idx])
                        all_trans.append(translated)

                    # Clear intermediate variables to free memory
                    del dict_data, clean_batch, preserved_batch
                    if 'parsed' in locals():
                        del parsed

                except Exception as exc:
                    logger.error("Translation batch error: %s", exc)
                    # fallback: append originals
                    all_trans.extend(batch)

                # Record performance and update monitoring
                batch_time = time.time() - batch_start_time
                self._record_performance(batch_time)

                if i % 3 == 0:
                    monitor.update_peak()

                # Force cleanup every few batches if memory usage is high
                if i % (adaptive_batch_size * 3) == 0 and self._get_memory_usage_mb() > self._memory_threshold_mb:
                    self._force_garbage_collection()

        return all_trans

    def translate_document_multi_language(self, target_languages: List[str], is_pdf: bool) -> Dict[str, Document]:
        """
        Optimized multi-language translation that loads the document once
        and creates copies for each language to reduce memory usage and I/O overhead.
        """
        results = {}
        original_document = None

        try:
            initial_memory = self._get_memory_usage_mb()
            logger.info(f"Starting multi-language Word translation for {len(target_languages)} languages (Memory: {initial_memory:.1f}MB)")

            # Load the original document once
            logger.info(f"Loading original document: {self.docx_path}")
            original_document = Document(self.docx_path)

            load_memory = self._get_memory_usage_mb()
            logger.info(f"Original document loaded (Memory: {load_memory:.1f}MB)")

            for lang_index, target_language in enumerate(target_languages):
                logger.info(f"Processing language {lang_index + 1}/{len(target_languages)}: {target_language}")

                try:
                    # Create a copy of the document for this language
                    lang_document = self._create_document_copy(original_document)

                    # Translate the copy
                    translated_document = self._translate_document_in_place(lang_document, target_language, is_pdf)

                    if translated_document:
                        results[target_language] = translated_document
                        logger.info(f"Successfully translated document for {target_language}")
                    else:
                        logger.error(f"Failed to translate document for {target_language}")
                        results[target_language] = None

                    # Force cleanup after each language
                    self._force_garbage_collection()
                    current_memory = self._get_memory_usage_mb()
                    logger.info(f"Completed {target_language} (Memory: {current_memory:.1f}MB)")

                except Exception as e:
                    logger.error(f"Error translating to {target_language}: {e}")
                    results[target_language] = None

            final_memory = self._get_memory_usage_mb()
            logger.info(f"Multi-language Word translation complete. Memory: {initial_memory:.1f}MB -> {final_memory:.1f}MB")

            return results

        except Exception as e:
            logger.error(f"Error in multi-language Word translation: {e}")
            return {}
        finally:
            # Cleanup original document
            if original_document:
                del original_document
            self._force_garbage_collection()

    def _create_document_copy(self, original_document):
        """
        Create a copy of the document by saving to a temporary file and reloading.
        This is more memory efficient than deep copying the object structure.
        """
        import tempfile

        try:
            # Create a temporary file for the copy
            with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
                temp_path = temp_file.name

            # Save the original to the temporary file
            original_document.save(temp_path)

            # Load the copy from the temporary file
            copy_document = Document(temp_path)

            # Clean up the temporary file
            try:
                os.unlink(temp_path)
            except Exception as e:
                logger.warning(f"Could not delete temporary file {temp_path}: {e}")

            return copy_document

        except Exception as e:
            logger.error(f"Error creating document copy: {e}")
            return None

    def _translate_document_in_place(self, document, target_language: str, is_pdf: bool):
        """
        Translate a document object in place without reloading from file.
        """
        try:
            self.is_pdf_converted = is_pdf

            paragraph_count = len(document.paragraphs)
            table_count = len(document.tables)
            textbox_count = self._count_textboxes_simple(document)

            logger.info(f"Translating document for {target_language}: {paragraph_count} paragraphs, {table_count} tables, {textbox_count} textboxes")

            with self._memory_monitor_context(f"In-place translation for {target_language}") as monitor:
                # Translate paragraphs
                self._translate_paragraphs_enhanced(document.paragraphs, target_language)
                monitor.update_peak()

                # Translate tables
                self._translate_tables(document.tables, target_language)
                monitor.update_peak()

                # Translate textboxes
                self._translate_document_textboxes_enhanced(document, target_language)
                monitor.update_peak()

                # Translate headers and footers
                self._translate_headers_footers(document, target_language)
                monitor.update_peak()

            return document

        except Exception as e:
            logger.error(f"Error in in-place translation for {target_language}: {e}")
            return None

    def _translate_runs(self, runs, target_lang: str):
        """
        Generic run-by-run translator used for paragraphs, table cells, and textboxes.
        Preserves formatting elements and updates runs in place.
        """
        if not runs:
            return 0

        texts = []
        preserved_map = []
        indices = []

        for idx, run in enumerate(runs):
            txt = run.text or ""
            if not txt.strip() or self._should_not_translate(txt):
                texts.append(txt)
                preserved_map.append({})
                indices.append(idx)
            else:
                clean, preserved = self._preserve_formatting_elements(txt)
                texts.append(clean)
                preserved_map.append(preserved)
                indices.append(idx)

        if not any(t.strip() for t in texts):
            return 0

        translations = self._batch_translate(texts, target_lang)
        for idx, translated in enumerate(translations):
            restored = self._restore_formatting_elements(translated, preserved_map[idx])
            if translated.strip():
                runs[indices[idx]].text = restored
            else:
                # Mantieni il testo originale se la traduzione è vuota
                runs[indices[idx]].text = runs[indices[idx]].text

        return len(translations)

    # ---------------------- Paragraphs, Tables, Headers ---------------------- #
    def _is_toc_or_index(self, paragraph: Paragraph) -> bool:
        try:
            for run in paragraph.runs:
                if run._element.xpath('.//w:fldChar[@w:fldCharType="begin"]'):
                    fld_code = run._element.xpath('.//w:instrText')
                    if fld_code and any(code.text and ("TOC" in code.text.upper() or "INDEX" in code.text.upper()) for code in fld_code):
                        return True
            if paragraph.style and paragraph.style.name and paragraph.style.name.lower() in ("toc", "table of contents", "index"):
                return True
        except Exception:
            # keep original behavior - if we can't inspect runs, assume not TOC
            return False
        return False

    def _translate_paragraphs_enhanced(self, paragraphs, target_lang: str):
        """Run-by-run translation for paragraphs with PDF-aware handling."""
        logger.info("Using run-by-run translation mode")
        for paragraph in paragraphs:
            if not paragraph.text.strip():
                continue

            if self._is_toc_or_index(paragraph):
                # Traduci il contenuto visibile del TOC senza toccare i codici di campo
                self._translate_toc([paragraph], target_lang)
                continue
            runs = paragraph.runs
            if not runs:
                continue
            self._translate_runs(runs, target_lang)

    def _translate_toc(self, paragraphs, target_lang):

        """
        Trabslation of indexes and TOC
        """

        runs_to_translate = []
        original_texts = []

        for p in paragraphs:
            for run in p.runs:
                # Salta i codici di campo
                if run._element.xpath('.//w:fldChar') or run._element.xpath('.//w:instrText'):
                    continue
                if run.text.strip():
                    runs_to_translate.append(run)
                    original_texts.append(run.text)

        if not original_texts:
            return

        translations = self._batch_translate(original_texts, target_lang)

        for run, translated in zip(runs_to_translate, translations):
            if translated.strip():
                run.text = translated

    def _translate_tables(self, tables, target_lang: str):
        """Translate all text runs in tables."""
        for table in tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        if not paragraph.text.strip() or self._is_toc_or_index(paragraph):
                            continue
                        self._translate_runs(paragraph.runs, target_lang)

    def _translate_headers_footers(self, document: Document, target_lang: str):
        for section in document.sections:
            for hf in (section.header, section.footer):
                if hf:
                    self._translate_paragraphs_enhanced(hf.paragraphs, target_lang)
                    self._translate_tables(hf.tables, target_lang)
                    # translate textboxes in headers/footers
                    try:
                        if hf._element is not None:
                            self._translate_textboxes_comprehensive(hf._element, target_lang)
                    except Exception as exc:
                        logger.debug("Error translating header/footer textboxes: %s", exc)

    # ---------------------- Textbox handling ---------------------- #
    def _translate_textbox_with_enhanced_adaptation(self, textbox_element, target_lang: str) -> int:
        """
        Translate textual w:t elements inside a textbox element. Adjusts size heuristically.
        """
        texts = []
        elements = []
        preserved = []
        original_combined = []

        try:
            for elem in textbox_element.iter():
                if elem.tag == qn("w:t") and elem.text and elem.text.strip():
                    t = elem.text
                    if self._should_not_translate(t):
                        clean, pres = t, {}
                    else:
                        clean, pres = self._preserve_formatting_elements(t)
                    texts.append(clean)
                    preserved.append(pres)
                    elements.append(elem)
                    original_combined.append(t)

            if not texts:
                return 0

            translations = self._batch_translate(texts, target_lang)
            translated_combined = []

            for elem, tr, pres in zip(elements, translations, preserved):
                restored = self._restore_formatting_elements(tr, pres)
                if isinstance(restored, str) and restored.strip():
                    elem.text = restored
                translated_combined.append(restored)

            # Adjust box dims based on ratio
            self._adjust_textbox_size(
                textbox_element, " ".join(original_combined).strip(), " ".join(translated_combined).strip()
            )

            return len(elements)
        except Exception as exc:
            logger.debug("Error translating textbox with enhanced adaptation: %s", exc)
            return 0

    def _translate_textboxes_comprehensive(self, element, target_lang: str) -> int:
        """
        Single XPath pass to find textbox-like containers and translate them.
        Uses a union XPath so we don't do multiple passes.
        """
        count = 0
        # union XPath covering common textbox containers
        xpath_union = (
            './/*[local-name()="txbxContent"] '
            '| .//*[contains(local-name(), "textbox")] '
            '| .//*[contains(local-name(), "txbx")] '
            '| .//*[contains(local-name(), "shape")] '
            '| .//*[contains(local-name(), "wsp")]'
        )
        try:
            textboxes = element.xpath(xpath_union)
            logger.debug("Found %s textbox-like elements", len(textboxes))
            for tb in textboxes:
                try:
                    c = self._translate_textbox_with_enhanced_adaptation(tb, target_lang)
                    count += c
                except Exception as inner:
                    logger.debug("Error translating one textbox: %s", inner)
                    continue
        except Exception as exc:
            logger.debug("Pattern search for textboxes failed: %s", exc)
        return count

    def _extract_and_translate_textbox_text_enhanced(self, document: Document, target_lang: str) -> int:
        """
        Group contiguous w:t elements by their textbox container and translate combined content
        for PDF converted documents (safer version: translate per-element in batch to avoid losing fragments).
        """
        try:
            doc_element = document._element
            textbox_map = {}
            # find all w:t and group by nearest textbox/shape ancestor
            for elem in doc_element.iter():
                if elem.tag == qn("w:t") and elem.text and elem.text.strip():
                    parent = elem.getparent()
                    textbox_container = None
                    while parent is not None:
                        parent_tag = parent.tag.split("}")[-1] if "}" in parent.tag else parent.tag
                        if any(k in parent_tag.lower() for k in ("txbx", "txbxcontent", "textbox", "shape", "drawing", "wps")):
                            textbox_container = parent
                            break
                        parent = parent.getparent()

                    if textbox_container is not None:
                        cid = id(textbox_container)
                        textbox_map.setdefault(cid, {"container": textbox_container, "elements": [], "texts": []})
                        textbox_map[cid]["elements"].append(elem)
                        textbox_map[cid]["texts"].append(elem.text.strip())

            if not textbox_map:
                logger.info("No textbox groups found")
                return 0

            total_translated = 0

            for group in textbox_map.values():
                elements = group["elements"]
                texts = group["texts"]
                if not texts:
                    continue

                # Translate per element (keeps mapping stable). _batch_translate already batches internally.
                translations = self._batch_translate(texts, target_lang)

                # Apply translations element-wise, but do NOT overwrite with empty translation.
                translated_count = 0
                for elem, tr in zip(elements, translations):
                    if isinstance(tr, str) and tr.strip():
                        elem.text = self._restore_formatting_elements(tr, {})  # restored formatting already handled in batch
                        translated_count += 1
                    else:
                        # keep original text if translation is empty / whitespace
                        continue

                total_translated += translated_count

                # heuristic size adjust (give it combined strings)
                translated_combined = " ".join([t for t in translations if isinstance(t, str) and t.strip()]) or ""
                self._adjust_textbox_size(group["container"], " ".join(texts).strip(), translated_combined.strip())

            logger.info("Successfully translated %s textbox text elements in %s textbox groups",
                        total_translated, len(textbox_map))
            return total_translated

        except Exception as exc:
            logger.error("Error in _extract_and_translate_textbox_text_enhanced: %s", exc)
            logger.error("Traceback: %s", traceback.format_exc())
            return 0

    def _adjust_textbox_size(self, textbox_element, original_text: str, translated_text: str):
        """
        Adjust textbox dimensions based on translated text
        
        Args:
            textbox_element: XML element of textbox
            original_text: Original text
            translated_text: Translated text
        """
        try:
            # Calculate length ratio between original and translated text
            if not original_text or len(original_text.strip()) == 0:
                return
                
            length_ratio = len(translated_text) / len(original_text)
            
            # If text is significantly longer, try to adapt textbox
            if length_ratio > 1.2:  # If text is 20% longer
                # Look for textbox dimension elements
                for elem in textbox_element.iter():
                    # Look for width attributes
                    if 'w' in elem.attrib:
                        try:
                            current_width = int(elem.attrib['w'])
                            # Increase width proportionally (max 50% increase)
                            new_width = int(current_width * min(length_ratio, 1.5))
                            elem.attrib['w'] = str(new_width)
                            logger.debug(f"Adjusted textbox width from {current_width} to {new_width}")
                        except (ValueError, KeyError):
                            continue
                    
                    # Look for height attributes if necessary
                    if length_ratio > 1.5 and 'h' in elem.attrib:
                        try:
                            current_height = int(elem.attrib['h'])
                            new_height = int(current_height * min(length_ratio * 0.8, 1.3))
                            elem.attrib['h'] = str(new_height)
                            logger.debug(f"Adjusted textbox height from {current_height} to {new_height}")
                        except (ValueError, KeyError):
                            continue
                            
        except Exception as e:
            logger.debug(f"Error adjusting textbox size: {e}")

    def _translate_document_textboxes_enhanced(self, document: Document, target_lang: str) -> int:
        """
        Try multiple strategies to translate textboxes with dimension adaptation.
        The function will apply same translation approach to headers/footers if successful.
        """
        logger.info("Starting enhanced textbox translation with dimension adaptation")
        strategies = [
            ("Comprehensive XPath", lambda: self._translate_textboxes_comprehensive(document._body._element, target_lang)),
            ("Enhanced text extraction", lambda: self._extract_and_translate_textbox_text_enhanced(document, target_lang)),
        ]
        for name, func in strategies:
            try:
                logger.info("Trying strategy: %s", name)
                count = func()
                if count > 0:
                    logger.info("Strategy '%s' successful: %s elements processed", name, count)
                    # Apply to headers/footers as well
                    for section in document.sections:
                        if section.header:
                            self._translate_textboxes_comprehensive(section.header._element, target_lang)
                        if section.footer:
                            self._translate_textboxes_comprehensive(section.footer._element, target_lang)
                    return count
                else:
                    logger.info("Strategy '%s' found no textboxes", name)
            except Exception as exc:
                logger.warning("Strategy '%s' failed: %s", name, exc)
                continue

        logger.warning("All textbox translation strategies failed")
        return 0

    def _count_textboxes_simple(self, document: Document) -> int:
        """Count probable textbox elements with a simple iteration (fast)."""
        total = 0
        try:
            for elem in document._body._element.iter():
                tag_name = elem.tag.split("}")[-1] if "}" in elem.tag else elem.tag
                if any(k in tag_name.lower() for k in ("txbxcontent", "textbox", "txbx")):
                    total += 1
            for section in document.sections:
                for hf in (section.header, section.footer):
                    if hf:
                        for elem in hf._element.iter():
                            tag_name = elem.tag.split("}")[-1] if "}" in elem.tag else elem.tag
                            if any(k in tag_name.lower() for k in ("txbxcontent", "textbox", "txbx")):
                                total += 1
        except Exception as exc:
            logger.debug("Error counting textboxes: %s", exc)
        return total

    # ---------------------- Combining & Distribution ---------------------- #
    def _combine_texts_intelligently(self, texts: List[str]) -> str:
        """
        Combine textual fragments conservatively:
        - merge hyphenated splits
        - join fragments that likely continue previous fragment
        """
        if not texts:
            return ""

        if len(texts) == 1:
            return self.text_merger.merge_hyphenated_words(texts[0].strip())

        combined_parts = [texts[0].strip()]
        for nxt in texts[1:]:
            nxt_s = nxt.strip()
            if not nxt_s:
                continue
            # Se l'ultimo pezzo termina con trattino → rimuovilo e unisci senza spazio
            if combined_parts[-1].endswith("-"):
                combined_parts[-1] = combined_parts[-1][:-1] + nxt_s
            else:
                # Mantieni frammenti separati come nel DOCX originale
                combined_parts.append(nxt_s)

        return " ".join(combined_parts)

    def _distribute_translation_across_paragraphs(self, paragraph_group: List[Paragraph], translated_text: str):
        """
        Split translated text by sentences and distribute across paragraph group.
        Conservative splitting using punctuation followed by whitespace.
        """
        if not paragraph_group:
            return

        sentences = re.split(r"(?<=[.!?])\s+", translated_text)
        if len(sentences) <= 1 or len(paragraph_group) <= 1:
            paragraph_group[0].text = translated_text
            for p in paragraph_group[1:]:
                p.text = ""
            return

        per = max(1, len(sentences) // len(paragraph_group))
        for i, p in enumerate(paragraph_group):
            start = i * per
            end = start + per
            if i == len(paragraph_group) - 1:
                end = len(sentences)
            p.text = " ".join(sentences[start:end]).strip()

    def _apply_translation_to_enhanced_group(self, paragraph_group: List[Paragraph], original_combined: str, translated_text: str):
        """
        Apply translated text to a paragraph group (PDF-converted or native).
        For PDF converted groups, we place text in first paragraph and clear others,
        unless the translated text is significantly larger in which case we distribute.
        """
        if not paragraph_group:
            return
        if len(paragraph_group) == 1:
            paragraph_group[0].text = translated_text
            return

        if self.is_pdf_converted:
            ratio = len(translated_text) / max(len(original_combined), 1)
            if ratio > 1.5:
                self._distribute_translation_across_paragraphs(paragraph_group, translated_text)
            else:
                paragraph_group[0].text = translated_text
                for p in paragraph_group[1:]:
                     if not p.text.strip():  # Cancella solo se era già vuoto
                        p.text = "" 
        else:
            paragraph_group[0].text = translated_text
            for p in paragraph_group[1:]:
                 if not p.text.strip():
                    p.text = ""

    # ---------------------- Public Workflow ---------------------- #
    def translate_document(self, target_language: str, is_pdf: bool) -> Document:
        """
        Translate an entire Word document including tables, headers, footers, TOC,
        indexes and textboxes with intelligent fragmentation handling and memory optimization.
        """
        try:
            initial_memory = self._get_memory_usage_mb()
            logger.info("Loading document: %s (Initial memory: %.1fMB)", self.docx_path, initial_memory)

            document = Document(self.docx_path)

            # Basic analysis and language caching
            self.is_pdf_converted = is_pdf
            logger.info("PDF converted document: %s", self.is_pdf_converted)

            paragraph_count = len(document.paragraphs)
            table_count = len(document.tables)
            textbox_count = self._count_textboxes_simple(document)

            load_memory = self._get_memory_usage_mb()
            logger.info(
                "Starting translation of %s paragraphs, %s tables, and %s textboxes (Memory after load: %.1fMB)",
                paragraph_count, table_count, textbox_count, load_memory
            )

            with self._memory_monitor_context("Word document translation") as monitor:
                logger.info("Translating paragraphs with enhanced PDF fragmentation handling")
                self._translate_paragraphs_enhanced(document.paragraphs, target_language)
                monitor.update_peak()
                self._force_garbage_collection()

                logger.info("Translating tables")
                self._translate_tables(document.tables, target_language)
                monitor.update_peak()
                self._force_garbage_collection()

                logger.info("Translating textboxes with enhanced size adaptation")
                translated_textbox_count = self._translate_document_textboxes_enhanced(document, target_language)
                if translated_textbox_count > 0:
                    logger.info("Successfully translated %s textbox elements with dimension adaptation", translated_textbox_count)
                else:
                    logger.warning("No textboxes were translated")
                monitor.update_peak()
                self._force_garbage_collection()

                logger.info("Translating headers and footers")
                self._translate_headers_footers(document, target_language)
                monitor.update_peak()

            logger.info("Translation completed successfully!")
            if self.is_pdf_converted:
                logger.info("Applied enhanced PDF-aware fragmentation handling for superior translation quality")
                logger.info("Used intelligent text merging to reconstruct fragmented sentences")


            return document

        except Exception as exc:
            logger.error("Error during translation: %s", exc)
            logger.error("Full traceback: %s", traceback.format_exc())
            return None

    def write_result_to_file(self, document: Document, lang: str):
        """
        Save the translated Document object into a new file named with language suffix.
        """
        logger.info("Writing results to Word file for language: %s", lang)
        original_base, original_ext = os.path.splitext(self.docx_path)
        translated_path = f"{original_base}_{lang}{original_ext}"
        directory = os.path.dirname(translated_path)
        if directory and not os.path.exists(directory):
            logger.info("Creating directory: %s", directory)
            os.makedirs(directory, exist_ok=True)

        try:
            document.save(translated_path)
            logger.info("Successfully saved translated Word file: %s", translated_path)
            if self.is_pdf_converted:
                logger.info("Document was converted from PDF - applied enhanced fragmentation handling for improved translation")
                logger.info("Text fragments were intelligently merged and distributed for better readability")
            logger.info("Note: You may need to manually update TOC and Index fields in Word after opening the translated document")
        except Exception as exc:
            logger.error("Error saving translated Word file: %s", exc)
            raise

    def convert_to_word(self):
        """
        Convert PDF (same base name) to DOCX using pdf2docx with memory optimization.
        """
        initial_memory = self._get_memory_usage_mb()
        logger.info("Converting PDF to Word using pdf2docx (Initial memory: %.1fMB)", initial_memory)

        original_base, _ = os.path.splitext(self.docx_path)
        pdf_input = f"{original_base}.pdf"
        docx_output = f"{original_base}.docx"
        self.original_name = self.docx_path

        if not os.path.exists(pdf_input):
            logger.error("PDF file not found: %s", pdf_input)
            raise FileNotFoundError(f"PDF file not found: {pdf_input}")

        # Get PDF file size for optimization
        pdf_size_mb = self._get_file_size_mb(pdf_input)
        logger.info("PDF file size: %.1fMB", pdf_size_mb)

        os.makedirs(os.path.dirname(docx_output) or ".", exist_ok=True)
        cv = None

        try:
            with self._memory_monitor_context(f"PDF to Word conversion ({pdf_size_mb:.1f}MB)") as monitor:
                logger.info("Starting PDF to DOCX conversion: %s", pdf_input)
                cv = Converter(pdf_input)

                # For large PDFs, consider page-by-page conversion to reduce memory usage
                if pdf_size_mb > 50:  # Large PDF
                    logger.info("Large PDF detected, using memory-optimized conversion")
                    # Note: pdf2docx doesn't support streaming conversion, but we can monitor memory
                    cv.convert(docx_output, start=0, end=None, layout_mode="exact")
                else:
                    cv.convert(docx_output, start=0, end=None, layout_mode="exact")

                cv.close()
                cv = None  # Clear reference immediately
                monitor.update_peak()

                # Force garbage collection after conversion
                self._force_garbage_collection()

            if os.path.exists(docx_output):
                conversion_memory = self._get_memory_usage_mb()
                docx_size_mb = self._get_file_size_mb(docx_output)
                logger.info("Successfully saved converted Word file: %s (%.1fMB, Memory: %.1fMB)",
                           docx_output, docx_size_mb, conversion_memory)

                # Clean up PDF file
                try:
                    os.remove(pdf_input)
                    logger.info("Successfully deleted PDF file: %s", pdf_input)
                except Exception as del_exc:
                    logger.warning("Could not delete PDF file %s: %s", pdf_input, del_exc)
            else:
                logger.error("DOCX file not found at expected location: %s", docx_output)
                raise Exception("Conversion failed: DOCX file not created")

            self.docx_path = docx_output
            self.is_pdf_converted = True

            # Update memory optimization settings based on converted file size
            self._initialize_memory_optimization(docx_output)

            final_memory = self._get_memory_usage_mb()
            logger.info("PDF conversion complete. Memory: %.1fMB -> %.1fMB", initial_memory, final_memory)

            return docx_output, self.is_pdf_converted

        except Exception as exc:
            logger.error("Error converting PDF to Word with pdf2docx: %s", exc)
            # Force cleanup on error
            self._force_garbage_collection()
            raise Exception(f"pdf2docx conversion failed: {exc}")
        finally:
            try:
                if cv:
                    cv.close()
            except Exception:
                pass

    def get_translation_stats(self) -> Dict[str, object]:
        """Return statistics about the (current) document path."""
        try:
            document = Document(self.docx_path)
            non_empty = [p for p in document.paragraphs if p.text.strip()]
            stats = {
                "total_paragraphs": len(document.paragraphs),
                "non_empty_paragraphs": len(non_empty),
                "average_paragraph_length": (sum(len(p.text) for p in non_empty) / len(non_empty)) if non_empty else 0,
                "short_paragraphs": len([p for p in non_empty if len(p.text) < 50]),
                "total_tables": len(document.tables),
                "textbox_count": self._count_textboxes_simple(document),
                "is_pdf_converted": self.is_pdf_converted,
                "detected_language": getattr(self, "document_language", "unknown"),
                "file_size_bytes": os.path.getsize(self.docx_path) if os.path.exists(self.docx_path) else 0,
                "fragmentation_handling": "enhanced" if self.is_pdf_converted else "standard",
            }
            return stats
        except Exception as exc:
            logger.error("Error getting translation stats: %s", exc)
            return {"error": str(exc)}
