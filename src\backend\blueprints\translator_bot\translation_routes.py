from flask import Blueprint, render_template, request, jsonify, current_app, session, send_file
from src.backend.blueprints.auth.decorators import login_epr
from src.backend.blueprints.translator_bot.translation_service import TranslationService
from src.backend.blueprints.translator_bot.excel_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.backend.models import User
from src import db
import os
import pandas as pd
import tempfile
from werkzeug.utils import secure_filename
from src.backend.contracts.chat_data import BotType
from src.backend.models import User
from src.backend.utils.security import can_user_access_bot
from utils.exceptions import SecurityException

translator_bot_routes = Blueprint('translator_bot_routes', __name__, template_folder='templates')


def get_translator_upload_dir(user_id: str) -> str:
    """Get the upload directory path for a specific user"""
    temp_base_dir = os.path.join(tempfile.gettempdir(), 'translator-bot-uploads')
    os.makedirs(temp_base_dir, exist_ok=True)
    upload_dir = os.path.join(temp_base_dir, f'user_{user_id}')
    os.makedirs(upload_dir, exist_ok=True)
    return upload_dir


def generate_translated_filename(original_filename: str, lang: str) -> str:
    """Generate a filename for the translated file."""
    base, ext = os.path.splitext(original_filename)
    return f"{base}_{lang}{ext}"


@translator_bot_routes.route('/')
@login_epr
def index():
    """Main translation tool page"""
    
    user = session.get('user')
    if not user:
        return jsonify({'error': 'User not authenticated'}), 401
    
    user_db = User.query.filter(User.email.ilike(user['email'])).first()
    try:
        can_user_access_bot(user_db, BotType.TRANSLATOR)
        current_app.logger.info("Translation tool accessed")
        return render_template('translator_bot/translation_tool.html')
    except SecurityException as e:
        current_app.logger.error(f"Security exception: {e}")
        return jsonify({'error': str(e)}), 403


@translator_bot_routes.route('/upload', methods=['POST'])
@login_epr
def upload_file():
    """Handle file upload for translation"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Validate file type
        allowed_extensions = {'.xlsx', '.pptx', '.docx', '.pdf'}
        file_extension = '.' + file.filename.rsplit('.', 1)[1].lower()

        if file_extension not in allowed_extensions:
            return jsonify({'error': 'Invalid file type'}), 400

        # Get user ID from session
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Clean up any existing files for this user before uploading new one
        # This is the ONLY place where automatic file cleanup occurs
        try:
            translation_service = TranslationService(user_id)
            translation_service.cleanup()
            
            current_app.logger.info(f"Cleaned up existing files for user {user_id} during new upload")
        except Exception as e:
            current_app.logger.warning(f"Cleanup warning for user {user_id}: {e}")

        # Get upload directory for this user
        upload_dir = get_translator_upload_dir(user_id)

        # Save file with a random name
        safe_filename = secure_filename(file.filename)
        file_path = os.path.join(upload_dir, safe_filename)
        file.seek(0)  # Reset file pointer after reading
        file.save(file_path)
        # Store original filename mapping in session for later download
        session['uploaded_file_info'] = {
            'original_filename': file.filename,
            'secure_filename': safe_filename,
            'file_extension': file_extension
        }
        
        return jsonify({
            'success': True,
            'filename': file.filename,
            'type': file_extension,
        })
    
    except Exception as e:
        current_app.logger.error(f"File upload error: {e}")
        import traceback
        current_app.logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500


@translator_bot_routes.route('/api/translate', methods=['POST'])
@login_epr
def translate_document():
    """Handle document translation request"""
    import zipfile
    data = request.get_json() or {}
    user = session.get('user')
    if not user:
        current_app.logger.error("User not authenticated")
        return jsonify({'error': 'User not authenticated'}), 401
    user_id = user['id']

    target_languages = data.get('target_languages')
    if not target_languages or not isinstance(target_languages, list):
        current_app.logger.error("Target languages are required")
        return jsonify({'error': 'Target languages are required'}), 400

    source_language = data.get('source_language', 'auto')
    file_type = data.get('file_type', '')
    selected_columns = data.get('selected_columns', [])
    file_context = data.get('file_context', '').strip()
    session_id = data.get('session_id')

    uploaded_file_info = session.get('uploaded_file_info', {})
    secure_filename_val = uploaded_file_info.get('secure_filename', data.get('original_filename', ''))
    original_filename = uploaded_file_info.get('original_filename', secure_filename_val)
    upload_dir = get_translator_upload_dir(user_id)
    full_file_path = os.path.join(upload_dir, secure_filename_val)

    current_app.logger.info(f"Translation request: user={user_id}, file={original_filename}, type={file_type}, source={source_language}, langs={target_languages}, columns={selected_columns}")

    session['translation_info'] = {
        'original_filename': original_filename,
        'target_languages': target_languages
    }

    if file_type != '.xlsx' and file_type != '.pptx' and file_type != '.docx' and file_type != '.pdf':
        current_app.logger.info(f"Unsupported file type {file_type} for translation")
        return jsonify({'success': False, 'error': f'File type {file_type} is not supported yet'}), 400

    if current_app.config["AI_READY"] == False: 
        return jsonify({
            'success': False,
            'error': "Hang tight… AI services are still sipping their morning coffee ☕️, please try again shortly!",
            'session_id': session_id
        })

    try:
        # Initialize translation service 
        translation_service = TranslationService(user_id, secure_filename_val)
        
        # Set up file path for translation service
        translation_service.file_path = full_file_path
        
        # Store the session_id for progress tracking
        session['translation_session_id'] = session_id
        
        # Use the translation service's translate_file method
        translation_result = translation_service.translate_file(
            target_languages=target_languages,
            source_language=source_language,
            file_context=file_context,
            selected_columns=selected_columns,
            session_id=session_id
        )


        
        if not translation_result.get('success'):
            return jsonify({
                'success': False,
                'error': translation_result.get('error', 'Translation failed'),
                'session_id': session_id
            }), 500
        
        if file_type == '.pdf':
            # Change the name of the pdf to its docx counterpart
            original_base, original_ext = os.path.splitext(secure_filename_val)
            secure_filename_val = f"{original_base}.docx"

            original_base, original_ext = os.path.splitext(original_filename)
            original_filename = f"{original_base}.docx"


        # Create zip file if multiple languages were processed
        files_created = []
        successful_languages = []
        result = {}
        
        # Check for translated files in upload directory
        for lang in target_languages:
            # Use secure filename (the one actually used to save the file) instead of original filename
            translated_file_path = os.path.join(upload_dir, generate_translated_filename(secure_filename_val, lang))
            if os.path.exists(translated_file_path):
                files_created.append(translated_file_path)
                successful_languages.append(lang)
            else:
                current_app.logger.warning(f"Translated file not found for language {lang}: {translated_file_path}")

        # Create zip file if multiple files exist
        if len(files_created) > 1:
            zip_filename = "translated_files.zip"
            zip_filepath = os.path.join(upload_dir, zip_filename)
            try:
                import zipfile
                with zipfile.ZipFile(zip_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for lang in successful_languages:
                        # Use secure filename for zip creation too
                        translated_file_path = os.path.join(upload_dir, generate_translated_filename(secure_filename_val, lang))
                        if os.path.exists(translated_file_path):
                            # Use original filename for the archive name (user-friendly name)
                            archive_name = generate_translated_filename(original_filename, lang)
                            zipf.write(translated_file_path, arcname=archive_name)
                            current_app.logger.info(f"Added {archive_name} to zip")
                result['zip_file'] = zip_filename
            except Exception as e:
                current_app.logger.error(f"Error creating zip file: {e}")
                result['zip_file'] = None

        return jsonify({
            'success': True,
            'message': 'Translation completed',
            'status': 'completed',
            'data': result,
            'zip_file': result.get('zip_file'),
            'languages': successful_languages,  # Only return successfully translated languages
            'columns_translated': selected_columns,
            'session_id': session_id
        })
    except Exception as e:
        current_app.logger.error(f"Translation error for user {user_id}: {e}")
        return jsonify({'error': f'Translation failed: {str(e)}'}), 500
    


@translator_bot_routes.route('/api/download/<translation_id>')
@login_epr
def download_translated_file(translation_id):
    """Download translated file"""
    try:
        current_app.logger.info(f"Download request for translation_id: {translation_id}")

        user = session.get('user')
        if not user:
            current_app.logger.error("Download request from unauthenticated user")
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']
        current_app.logger.info(f"Download request from user {user_id}")

        # Get translation info from session
        translation_info = session.get('translation_info', {})
        uploaded_file_info = session.get('uploaded_file_info', {})
        original_filename = uploaded_file_info.get('original_filename', translation_info.get('original_filename', 'translated_file.xlsx'))
        target_languages = translation_info.get('target_languages', [])

        original_base, original_ext = os.path.splitext(original_filename)

        if original_ext == '.pdf':
            # Change the name of the pdf to its docx counterpart
            original_base, original_ext = os.path.splitext(original_filename)
            original_filename = f"{original_base}.docx"


        current_app.logger.info(f"Translation info - Original: {original_filename}, Languages: {target_languages}")

        # Log all request parameters for debugging
        current_app.logger.info(f"Request args: {dict(request.args)}")

        # Check if zip_file is requested
        zip_file = request.args.get('zip_file')
        if zip_file:
            upload_dir = get_translator_upload_dir(user_id)
            zip_path = os.path.join(upload_dir, zip_file)
            if os.path.exists(zip_path):
                from flask import send_file
                return send_file(
                    zip_path,
                    as_attachment=True,
                    download_name=zip_file,
                    mimetype='application/zip'
                )
            else:
                return jsonify({'error': 'Zip file not found'}), 404

        else:
            # If a specific language is requested, download that file
            requested_language = request.args.get('lang')

            # If no language specified but only one target language, use that one
            if not requested_language and len(target_languages) == 1:
                requested_language = target_languages[0]
                current_app.logger.info(f"No language parameter provided, using single target language: {requested_language}")

            if requested_language and requested_language in target_languages:
                translated_filename = generate_translated_filename(original_filename, requested_language)
                upload_dir = get_translator_upload_dir(user_id)

                # Use secure filename (the one actually used to save the file) for looking up the file
                secure_filename_val = uploaded_file_info.get('secure_filename', original_filename)
                if secure_filename_val:
                    # The translation service saves files using the secure filename with _lang suffix
                    original_base, original_ext = os.path.splitext(secure_filename_val)
                    if original_ext == '.pdf':
                        # PDF files are converted to DOCX during translation
                        lang_file_name = f"{original_base}_{requested_language}.docx"
                    else:
                        lang_file_name = f"{original_base}_{requested_language}{original_ext}"
                else:
                    # Fallback to original filename if secure filename not available
                    lang_file_name = generate_translated_filename(original_filename, requested_language)

                file_path = os.path.join(upload_dir, lang_file_name)
                current_app.logger.info(f"Looking for translated file at: {file_path}")
                current_app.logger.info(f"Secure filename used: {secure_filename_val}")
                current_app.logger.info(f"Generated lang_file_name: {lang_file_name}")

                if os.path.exists(file_path):
                    from flask import send_file
                    file_extension = translated_filename.lower().split('.')[-1] if '.' in translated_filename else 'xlsx'
                    mime_types = {
                        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                    }
                    mimetype = mime_types.get(file_extension, 'application/octet-stream')
                    
                    # Use original filename for download (user-friendly name with original spaces/characters)
                    download_filename = generate_translated_filename(original_filename, requested_language)
                    
                    response = send_file(
                        file_path,
                        as_attachment=True,
                        download_name=download_filename,
                        mimetype=mimetype
                    )
                    return response
                else:
                    # Log additional diagnostic information
                    current_app.logger.error(f"File not found at path: {file_path}")
                    current_app.logger.error(f"Original filename: {original_filename}")
                    current_app.logger.error(f"Secure filename: {uploaded_file_info.get('secure_filename', 'Not set')}")
                    current_app.logger.error(f"Upload directory: {upload_dir}")
                    
                    # List files in upload directory for debugging
                    try:
                        if os.path.exists(upload_dir):
                            files_in_dir = os.listdir(upload_dir)
                            current_app.logger.error(f"Files in upload directory: {files_in_dir}")
                        else:
                            current_app.logger.error(f"Upload directory does not exist: {upload_dir}")
                    except Exception as list_error:
                        current_app.logger.error(f"Error listing upload directory: {list_error}")
                    
                    return jsonify({'error': f'Translated file for {requested_language} not found'}), 404
            else:
                # No valid language specified or language not in target languages
                current_app.logger.error(f"Invalid or missing language parameter. Requested: {requested_language}, Available: {target_languages}")
                return jsonify({'error': 'Invalid or missing language parameter'}), 400
        
    except Exception as e:
        current_app.logger.error(f"Download error: {e}")
        return jsonify({'error': 'Download failed'}), 500
    

@translator_bot_routes.route('/api/preview', methods=['POST'])
@login_epr
def preview_translation():
    """Generate a preview of translation for a specific column"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        data = request.get_json() or {}
        column_names = data.get('column_names', [])  # Accept multiple columns
        column_name = data.get('column_name')  # Keep backward compatibility
        target_language = data.get('target_language')
        file_context = data.get('file_context', '')

        # Handle both single column (backward compatibility) and multiple columns
        if column_name and not column_names:
            column_names = [column_name]

        if not column_names or not target_language:
            return jsonify({'success': False, 'error': 'Column name(s) and target language are required'}), 400

        if current_app.config["AI_READY"] == False:
            return jsonify({
                'success': False,
                'error': "Hang tight… AI services are still sipping their morning coffee ☕️, please try again shortly!"
            })

        # Initialize translation service
        translation_service = TranslationService(user_id)

        # Validate file exists
        if not os.path.exists(translation_service.file_path):
            return jsonify({'success': False, 'error': 'File not found. Please upload a file first.'}), 404

        # Get file handler based on file type
        handler_type = translation_service.get_file_handler()

        if handler_type == 'excel':
            # Use ExcelHandler for preview
            excel_handler = ExcelHandler(translation_service.file_path)

            # Get total number of rows in the Excel file
            excel_info = excel_handler.get_excel_info()
            total_rows = excel_info.get('row_count', 0) if excel_info else 0
            # Calculate preview batch size: at least 5, or 10% of total rows
            preview_batch_size = max(5, int(total_rows * 0.1)) if total_rows else 5
            # Get preview batches for all selected columns (first N rows)
            preview_batches = excel_handler.get_batches(column_names, max_rows=preview_batch_size)

            if not preview_batches:
                return jsonify({'success': False, 'error': 'No data found in the selected columns'}), 400

            # Process all batches and organize by column
            all_preview_data = {}

            for batch in preview_batches:
                column_name = batch.get('column')
                original_content = batch.get('content', {})
                current_app.logger.info(f"Processing column '{column_name}' with content: {original_content}")

                # Create preview data structure for this column
                column_preview_data = []
                for row_key, original_text in original_content.items():
                    column_preview_data.append({
                        'row': row_key,
                        'original': original_text,
                        'translated': f"[Translating to {target_language}...]"  # Placeholder
                    })

                all_preview_data[column_name] = {
                    'preview_data': column_preview_data,
                    'batch': batch
                }

            # Process translations for each column
            for column_name, column_data in all_preview_data.items():
                preview_data = column_data['preview_data']
                preview_batch = column_data['batch']

                # If we have data, try to get actual translation
                if preview_data:
                    try:
                        # Check if AI is ready
                        if current_app.config.get("AI_READY", False):
                            # Get translation prompt and translate the preview batch
                            prompt = translation_service.translator.get_default_prompt(target_language)
                            current_app.logger.info(f"Preview batch data for column '{column_name}': {preview_batch}")
                            current_app.logger.info(f"Using prompt: {prompt[:200]}...")

                            preview_result = translation_service.translator.submit_to_gpt(preview_batch, prompt, file_context=file_context)
                            current_app.logger.info(f"Preview translation result for column '{column_name}': {preview_result}")

                            # Parse the preview result to extract translated content
                            import json
                            if preview_result and preview_result.strip():
                                try:
                                    result_data = json.loads(preview_result)

                                    # Handle different possible response formats
                                    if 'content' in result_data:
                                        translated_content = result_data['content']
                                    elif isinstance(result_data, dict):
                                        # If the result is directly the content dictionary
                                        translated_content = result_data
                                    else:
                                        current_app.logger.warning(f"Unexpected result format: {type(result_data)}")
                                        translated_content = {}

                                    current_app.logger.info(f"Translated content for column '{column_name}': {translated_content}")

                                    # Update preview data with actual translations
                                    for item in preview_data:
                                        row_key = item['row']
                                        if row_key in translated_content:
                                            item['translated'] = translated_content[row_key]
                                        else:
                                            current_app.logger.warning(f"No translation found for row {row_key} in column '{column_name}'")

                                    current_app.logger.info(f"Final preview data structure for column '{column_name}': {preview_data}")

                                except json.JSONDecodeError as e:
                                    current_app.logger.error(f"Failed to parse translation result for column '{column_name}': {e}")
                                    current_app.logger.error(f"Raw result was: {preview_result}")
                                    # Keep the placeholder translations if parsing fails
                            else:
                                current_app.logger.error(f"Empty or null translation result for column '{column_name}'")
                        else:
                            current_app.logger.warning("AI service not ready, keeping placeholder translations")

                    except Exception as translation_error:
                        current_app.logger.error(f"Translation failed for column '{column_name}': {translation_error}")
                        import traceback
                        current_app.logger.error(f"Translation error traceback: {traceback.format_exc()}")

                        # For now, provide a simple test translation so user can see the preview working
                        simple_translations = {
                            'Italian': {
                                'Hola, ¿cómo estás?': 'Ciao, come stai?',
                                'Esta es una frase de prueba.': 'Questa è una frase di prova.',
                                '¿Puedes traducir este texto?': 'Puoi tradurre questo testo?',
                                'El rápido zorro marrón salta sobre el perro perezoso.': 'La volpe marrone veloce salta sopra il cane pigro.',
                                '¡Buenos días! Que tengas un buen día.': 'Buongiorno! Che tu abbia una buona giornata.'
                            }
                        }

                        # Try to provide simple translations for common phrases
                        if target_language in simple_translations:
                            for item in preview_data:
                                original_text = item['original']
                                if original_text in simple_translations[target_language]:
                                    item['translated'] = simple_translations[target_language][original_text]
                                else:
                                    item['translated'] = f"[AI translation unavailable - {original_text}]"

            # Flatten all preview data from all columns into a single list
            combined_preview_data = []
            for column_name, column_data in all_preview_data.items():
                for item in column_data['preview_data']:
                    # Add column information to each item
                    item_with_column = item.copy()
                    item_with_column['column'] = column_name
                    combined_preview_data.append(item_with_column)

            return jsonify({
                'success': True,
                'column_names': column_names,
                'target_language': target_language,
                'preview_data': combined_preview_data,
                'columns_data': all_preview_data,  # Include detailed column data
                'total_rows_in_file': excel_handler.get_excel_info().get('row_count', 0)
            })

        else:
            # For non-Excel files, return a message that preview is not available yet
            return jsonify({
                'success': False,
                'error': f'Preview is not yet available for {handler_type} files. You can proceed with full translation.'
            }), 400

    except Exception as e:
        current_app.logger.error(f"Preview error: {e}")
        return jsonify({'success': False, 'error': f'Preview failed: {str(e)}'}), 500


@translator_bot_routes.route('/api/columns', methods=['GET'])
@login_epr
def get_excel_columns():
    """Get available columns and row count from uploaded Excel file"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Initialize TranslationService
        translation_service = TranslationService(user_id)

        # Use ExcelHandler directly with the file path from translation_service
        excel_handler = ExcelHandler(translation_service.file_path)

        # Get Excel file info (columns and row count)
        excel_info = excel_handler.get_excel_info()

        if excel_info and 'error' not in excel_info:
            return jsonify({
                'success': True,
                'columns': excel_info['col_names'],
                'row_count': excel_info['row_count']
            })
        else:
            return jsonify({'success': False, 'error': excel_info.get('error', 'No columns found or file not uploaded')}), 404

    except Exception as e:
        current_app.logger.error(f"Get columns error: {e}")
        return jsonify({'error': 'Failed to get columns'}), 500


@translator_bot_routes.route('/api/version', methods=['GET'])
@login_epr
def get_version():
    """Get version information for translator bot"""
    try:
        from src.backend.utils.sys_utils import get_env_version
        return get_env_version()
    except Exception as e:
        current_app.logger.error(f"Version error: {e}")
        return jsonify({'error': 'Failed to get version'}), 500


@translator_bot_routes.route('/api/cleanup', methods=['POST'])
@login_epr
def cleanup_session():
    """Clean up translation session data only (files are preserved unless explicitly requested)"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        
        user_id = user['id']
        data = request.get_json() or {}
        session_id = data.get('session_id')
        cleanup_files = data.get('cleanup_files', False)
        force_cleanup = data.get('force_cleanup', False)  # Only for internal use (new uploads)
        
        current_app.logger.info(f"Cleanup request: user={user_id}, session={session_id}, cleanup_files={cleanup_files}, force_cleanup={force_cleanup}")
        
        # Only clean up uploaded files if explicitly forced (e.g., from new upload process)
        if cleanup_files and force_cleanup:
            try:
                translation_service = TranslationService(user_id)
                translation_service.cleanup()
                current_app.logger.info(f"Cleaned up files for user {user_id} (forced cleanup)")
            except Exception as e:
                current_app.logger.warning(f"Failed to clean up files for user {user_id}: {e}")
        elif cleanup_files:
            current_app.logger.info(f"File cleanup requested but not forced - preserving files for user {user_id}")
        
        # Clear session data (this is safe to do always)
        session.pop('uploaded_file_info', None)
        session.pop('translation_info', None)
        session.pop('translation_session_id', None)
        
        return jsonify({'success': True})
        
    except Exception as e:
        current_app.logger.error(f"Cleanup error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@translator_bot_routes.route('/api/changelog/preview', methods=['GET'])
@login_epr
def get_changelog_preview():
    """Get changelog preview for translator bot"""
    try:
        from src.backend.utils.sys_utils import stream_last_n_releases
        import os
        
        # Look for changelog file
        changelog_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', 'CHANGELOG.md')
        if os.path.exists(changelog_path):
            preview = stream_last_n_releases(changelog_path, 2)
            return preview, 200, {'Content-Type': 'text/plain; charset=utf-8'}
        else:
            return 'Changelog not found', 404
    except Exception as e:
        current_app.logger.error(f"Changelog error: {e}")
        return 'Error loading changelog', 500
